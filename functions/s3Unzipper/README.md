# S3 Unzipper Lambda

Automatically monitors specific S3 bucket folders and extracts ZIP files uploaded to those folders.

## Features

- **Automatic monitoring** of `scorm/` and `html/` folders in S3 bucket
- **Automatic detection** of uploaded ZIP files
- **Automatic extraction** of all files from ZIP
- **Automatic upload** of extracted files to S3
- **Preserves directory structure** from ZIP
- **Automatic content-type** detection based on file extension
- **Detailed logging** of the entire process

## How it works

1. **Trigger**: When a ZIP file is uploaded to `scorm/` or `html/` folders
2. **Download**: Lambda downloads ZIP file to temporary directory
3. **Extraction**: Extracts all files from ZIP
4. **Upload**: Uploads each extracted file to S3
5. **Organization**: Files are saved in `folder/zip-name/`

## Usage Example

### Before:
```
bucket-name/
├── scorm/
│   └── course-scorm.zip
└── html/
    └── website.zip
```

### After:
```
bucket-name/
├── scorm/
│   └── course-scorm/
│       ├── index.html
│       ├── scormdriver.js
│       └── content/
│           └── lesson1.html
└── html/
    └── website/
        ├── index.html
        ├── style.css
        ├── script.js
        └── images/
            └── logo.png
```

## Configuration

### Monitored folders
Lambda monitors `scorm/` and `html/` folders. To change or add other folders:

1. Edit `serverless.yml`:
```yaml
events:
  - s3:
      bucket: ${self:custom.bucketName}
      event: s3:ObjectCreated:*
      rules:
        - prefix: your-folder/  # Change here
        - suffix: .zip
```

### Delete original ZIP
**Configured**: Original ZIP file is **automatically deleted** after successful extraction.

## Lambda Settings

- **Timeout**: 300 seconds (5 minutes)
- **Memory**: 512 MB
- **Runtime**: Python 3.12

## Logs

To view logs in real-time:
```bash
serverless logs -f s3Unzipper --tail
```

## Limitations

- **Maximum size**: 512 MB memory (adjust if needed)
- **Timeout**: 5 minutes maximum per execution
- **Supported files**: ZIP only
- **Structure**: Preserves ZIP directory structure

## Required Permissions

Lambda needs the following S3 permissions:
- `s3:GetObject` - To download ZIP file
- `s3:PutObject` - To upload extracted files
- `s3:DeleteObject` - To delete original ZIP

## Supported Content Types

Lambda automatically detects content-type based on file extension:
- Images: jpg, png, gif, svg
- Documents: pdf, doc, docx, xls, xlsx
- Web: html, css, js, json, xml
- Media: mp4, mp3, wav
- Text: txt
- And others...
