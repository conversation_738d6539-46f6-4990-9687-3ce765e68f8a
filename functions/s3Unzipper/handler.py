import json
import boto3
import zipfile
import os
import tempfile
from urllib.parse import unquote_plus
from datetime import datetime


def lambda_handler(event, context):
    """
    Lambda that monitors S3 bucket and automatically extracts ZIP files
    """
    
    s3_client = boto3.client('s3')
    
    try:
        # Log received event
        print(f"Event received: {json.dumps(event)}")

        # Process each record from S3 event
        for record in event['Records']:
            # Check if it's an S3 event
            if record['eventSource'] != 'aws:s3':
                print(f"Skipping non-S3 event: {record['eventSource']}")
                continue

            # Get file information
            bucket_name = record['s3']['bucket']['name']
            object_key = unquote_plus(record['s3']['object']['key'])
            event_name = record['eventName']
            
            print(f"Processing S3 event: {event_name}")
            print(f"Bucket: {bucket_name}")
            print(f"Object: {object_key}")

            # Check if it's a ZIP file and a creation/upload event
            if not object_key.lower().endswith('.zip'):
                print(f"Skipping non-zip file: {object_key}")
                continue

            if not event_name.startswith('ObjectCreated'):
                print(f"Skipping non-creation event: {event_name}")
                continue

            # Process ZIP file
            process_zip_file(s3_client, bucket_name, object_key)
            
        return {
            "statusCode": 200,
            "body": json.dumps({
                "message": "ZIP files processed successfully",
                "timestamp": datetime.utcnow().isoformat()
            })
        }
        
    except Exception as e:
        error_message = f"Error processing S3 event: {str(e)}"
        print(error_message)
        
        return {
            "statusCode": 500,
            "body": json.dumps({
                "error": error_message,
                "timestamp": datetime.utcnow().isoformat()
            })
        }


def process_zip_file(s3_client, bucket_name, zip_object_key):
    """
    Downloads ZIP file, extracts files and uploads them back to S3
    """
    
    print(f"Starting to process ZIP file: {zip_object_key}")

    # Create temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        zip_file_path = os.path.join(temp_dir, 'archive.zip')

        try:
            # Download ZIP file from S3
            print(f"Downloading ZIP file from S3...")
            s3_client.download_file(bucket_name, zip_object_key, zip_file_path)
            print(f"ZIP file downloaded successfully")

            # Determine destination directory (remove .zip from name)
            base_path = zip_object_key.rsplit('.zip', 1)[0]
            extract_prefix = f"{base_path}/"

            # Extract ZIP file
            print(f"Extracting ZIP file...")
            extracted_files = []
            
            with zipfile.ZipFile(zip_file_path, 'r') as zip_ref:
                # List all files in ZIP
                file_list = zip_ref.namelist()
                print(f"Found {len(file_list)} files in ZIP")

                for file_info in zip_ref.infolist():
                    # Skip directories
                    if file_info.is_dir():
                        continue

                    # Extract file
                    extracted_path = zip_ref.extract(file_info, temp_dir)

                    # Calculate S3 destination path
                    # Remove problematic characters and normalize path
                    relative_path = file_info.filename
                    s3_destination_key = f"{extract_prefix}{relative_path}"

                    # Upload extracted file to S3
                    print(f"Uploading {relative_path} to {s3_destination_key}")

                    # Detect content type based on extension
                    content_type = get_content_type(relative_path)
                    
                    s3_client.upload_file(
                        extracted_path,
                        bucket_name,
                        s3_destination_key,
                        ExtraArgs={'ContentType': content_type}
                    )
                    
                    extracted_files.append(s3_destination_key)
            
            print(f"Successfully extracted {len(extracted_files)} files:")
            for file_key in extracted_files:
                print(f"  - {file_key}")

            # Delete original ZIP file after successful extraction
            s3_client.delete_object(Bucket=bucket_name, Key=zip_object_key)
            print(f"Original ZIP file deleted: {zip_object_key}")

        except zipfile.BadZipFile:
            print(f"Error: {zip_object_key} is not a valid ZIP file")
            raise
        except Exception as e:
            print(f"Error processing ZIP file {zip_object_key}: {str(e)}")
            raise


def get_content_type(filename):
    """
    Returns content type based on file extension
    """
    extension = filename.lower().split('.')[-1] if '.' in filename else ''
    
    content_types = {
        'txt': 'text/plain',
        'html': 'text/html',
        'css': 'text/css',
        'js': 'application/javascript',
        'json': 'application/json',
        'xml': 'application/xml',
        'pdf': 'application/pdf',
        'jpg': 'image/jpeg',
        'jpeg': 'image/jpeg',
        'png': 'image/png',
        'gif': 'image/gif',
        'svg': 'image/svg+xml',
        'mp4': 'video/mp4',
        'mp3': 'audio/mpeg',
        'wav': 'audio/wav',
        'zip': 'application/zip',
        'doc': 'application/msword',
        'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'xls': 'application/vnd.ms-excel',
        'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    }
    
    return content_types.get(extension, 'application/octet-stream')
