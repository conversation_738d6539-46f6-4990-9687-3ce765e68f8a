import boto3
import os

def lambda_handler(event, context):
    # Configure RDS client
    rds = boto3.client('rds', region_name=os.environ['REGION'])
    db_instance_id = os.environ['DB_INSTANCE_ID']
    action = event.get('action')

    try:
        if action == 'start':
            response = rds.start_db_instance(DBInstanceIdentifier=db_instance_id)
            return f"Instance {db_instance_id} started successfully."
        elif action == 'stop':
            response = rds.stop_db_instance(DBInstanceIdentifier=db_instance_id)
            return f"Instance {db_instance_id} stopped successfully."
        else:
            return "Invalid action. Use 'start' or 'stop'."
    except Exception as e:
        return f"Error: {str(e)}"
