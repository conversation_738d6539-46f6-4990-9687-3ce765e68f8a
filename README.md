# Keeps Lambda Services

AWS Lambda services for S3 bucket automation tasks.

## Available Lambdas

### S3 Unzipper
Automatically monitors `scorm/` and `html/` folders in S3 bucket and extracts ZIP files uploaded to these folders.

**Features:**
- Monitors `scorm/*.zip` and `html/*.zip`
- Automatically extracts all files
- Organizes files in folders based on ZIP name
- Deletes original ZIP file after extraction
- Automatically detects content-type

## Deployment

### Automatic Deployment (CI/CD)
- **develop** branch → **stage** environment
- **main** branch → **prod** environment

### Manual Deployment
```bash
# Stage environment
serverless deploy --stage stage

# Production environment
serverless deploy --stage prod
```

## Logs

To monitor logs in real-time:
```bash
serverless logs -f s3Unzipper --tail --stage dev
```

## Configuration

### Environment Variables
- `BUCKET_NAME` - S3 bucket name to monitor

### GitHub Secrets (for CI/CD)
Configure these as **Repository secrets** in GitHub:
- `AWS_ACCESS_KEY_ID` - AWS access key
- `AWS_SECRET_ACCESS_KEY` - AWS secret key
- `SERVERLESS_ACCESS_KEY` - Serverless Framework access token
- `BUCKET_NAME_STAGE` - S3 bucket name for stage environment
- `BUCKET_NAME_PROD` - S3 bucket name for production environment

#### How to obtain SERVERLESS_ACCESS_KEY

1. Access https://app.serverless.com/
2. Login with company account:
   - **Email:** <EMAIL>
   - **Password:** Contact CTO for credentials
3. Navigate to: Settings → Access Keys
4. Create a new Access Key
5. Copy the generated token
6. Add it as `SERVERLESS_ACCESS_KEY` secret in GitHub repository settings

#### GitHub Repository Secrets Configuration
Navigate to: `Settings` → `Secrets and variables` → `Actions` → **Secrets** tab
Add all secrets as **Repository secrets** (not Environment secrets or Variables)

## Project Structure

```
keeps-lambda-services/
├── functions/
│   └── s3Unzipper/
│       ├── handler.py
│       ├── requirements.txt
│       └── README.md
├── serverless.yml
├── .env
├── .gitignore
└── README.md
```

## Development

### Local testing
```bash
serverless invoke local --function s3Unzipper --data '{"Records":[{"eventSource":"aws:s3","s3":{"bucket":{"name":"test-bucket"},"object":{"key":"scorm/test.zip"}}}]}'
```

### Adding new Lambdas
1. Create a new folder in `functions/`
2. Add `handler.py` and `requirements.txt`
3. Configure in `serverless.yml`

