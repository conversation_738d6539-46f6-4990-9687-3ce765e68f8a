name: Deploy Lambda Services

on:
  push:
    branches:
      - develop    # Deploy to stage environment
      - main       # Deploy to production environment
  pull_request:
    branches:
      - develop
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.12'

      - name: Install Serverless Framework
        run: npm install -g serverless

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Determine deployment stage
        id: stage
        run: |
          if [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
            echo "stage=production" >> $GITHUB_OUTPUT
            echo "Deploying to PRODUCTION environment"
          elif [[ "${{ github.ref }}" == "refs/heads/develop" ]]; then
            echo "stage=stage" >> $GITHUB_OUTPUT
            echo "Deploying to STAGE environment"
          else
            echo "stage=dev" >> $GITHUB_OUTPUT
            echo "Deploying to DEV environment"
          fi

      - name: Deploy to Stage
        if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
        run: serverless deploy --stage stage
        env:
          BUCKET_NAME: ${{ secrets.BUCKET_NAME_STAGE }}
          SERVERLESS_ACCESS_KEY: ${{ secrets.SERVERLESS_ACCESS_KEY }}

      - name: Deploy to Production
        if: github.ref == 'refs/heads/main' && github.event_name == 'push'
        run: serverless deploy --stage production
        env:
          BUCKET_NAME: ${{ secrets.BUCKET_NAME_PROD }}
          SERVERLESS_ACCESS_KEY: ${{ secrets.SERVERLESS_ACCESS_KEY }}

      - name: Run tests (if any)
        if: github.event_name == 'pull_request'
        run: |
          echo "Running tests..."
          # Add your test commands here
          # python -m pytest tests/
          echo "No tests configured yet"

      - name: Post deployment info
        if: github.event_name == 'push'
        run: |
          echo "Deployment completed successfully!"
          echo "Stage: ${{ steps.stage.outputs.stage }}"
          echo "Branch: ${{ github.ref_name }}"
          echo "Commit: ${{ github.sha }}"
