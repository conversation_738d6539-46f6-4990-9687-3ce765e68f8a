# CI/CD Pipeline Configuration

This directory contains GitHub Actions workflows for automated deployment of Lambda services.

## Workflow: deploy.yml

Automatically deploys Lambda services based on branch:

### Branch Strategy
- **develop** → **stage** environment
- **main** → **production** environment

### Triggers
- **Push** to `develop` or `main` branches
- **Pull Request** to `develop` or `main` branches (runs tests only)

### Required Secrets

Configure these secrets in your GitHub repository settings:
**Location:** `Settings` → `Secrets and variables` → `Actions` → **Secrets** tab (Repository secrets)

#### AWS Credentials
- `AWS_ACCESS_KEY_ID` - AWS access key for deployment
- `AWS_SECRET_ACCESS_KEY` - AWS secret key for deployment

#### Serverless Framework
- `SERVERLESS_ACCESS_KEY` - Serverless Framework access token

#### Environment Variables
- `BUCKET_NAME_STAGE` - S3 bucket name for stage environment
- `BUCKET_NAME_PROD` - S3 bucket name for production environment

### Deployment Process

1. **Code checkout**
2. **Setup Node.js and Python**
3. **Install Serverless Framework**
4. **Configure AWS credentials**
5. **Determine deployment stage**
6. **Deploy to appropriate environment**
7. **Post deployment information**

### Manual Deployment

If you need to deploy manually:

```bash
# Deploy to stage
serverless deploy --stage stage

# Deploy to production
serverless deploy --stage production
```

### Adding Tests

To add tests to the pipeline, update the "Run tests" step in deploy.yml:

```yaml
- name: Run tests
  if: github.event_name == 'pull_request'
  run: |
    pip install -r requirements-dev.txt
    python -m pytest tests/
```
