# Organization ensures this service is used with the correct Serverless Framework Access Key
org: keeps
# Service name - will be added to AWS resource names
service: keeps-lambda-services

frameworkVersion: '4'

provider:
  name: aws
  runtime: python3.12
  region: us-east-1
  stage: ${opt:stage, 'stage'}
  tags:
    Project: Keeps Lambda Services
    Owner: <EMAIL>
    Environment: ${opt:stage, 'stage'}
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - logs:DeleteLogGroup
      Resource: '*'

custom:
  bucketName: ${env:BUCKET_NAME}

functions:
  s3Unzipper:
    handler: functions/s3Unzipper/handler.lambda_handler
    description: Lambda that monitors S3 bucket and automatically extracts ZIP files.
    timeout: 300
    memorySize: 512
    environment:
      BUCKET_NAME: ${env:BUCKET_NAME}
    iamRoleStatements:
      - Effect: Allow
        Action:
          - s3:Get*
          - s3:Put*
          - s3:List*
          - s3:Delete*
        Resource:
          - arn:aws:s3:::${self:custom.bucketName}
          - arn:aws:s3:::${self:custom.bucketName}/*
    package:
      individually: true
      patterns:
        - functions/s3Unzipper/**
    events:
      - s3:
          bucket: ${self:custom.bucketName}
          event: s3:ObjectCreated:*
          rules:
            - prefix: scorm/
            - suffix: .zip
          existing: true
      - s3:
          bucket: ${self:custom.bucketName}
          event: s3:ObjectCreated:*
          rules:
            - prefix: html/
            - suffix: .zip
          existing: true

  pauseResumeRDS:
    handler: functions/pauseResumeRDS/handler.lambda_handler
    description: Lambda that controls RDS instance start/stop based on EventBridge schedule.
    timeout: 60
    memorySize: 128
    environment:
      REGION: ${self:provider.region}
      STAGE: ${self:provider.stage}
      DB_INSTANCE_ID: ${env:DB_INSTANCE_ID}
    iamRoleStatements:
      - Effect: Allow
        Action:
          - rds:StartDBInstance
          - rds:StopDBInstance
          - rds:DescribeDBInstances
        Resource: '*'
    package:
      individually: true
      patterns:
        - functions/pauseResumeRDS/**
    stage: stage
    events:
      - eventBridge:
          schedule: cron(0 10 * * ? *)
          input:
            action: start
          description: "Start RDS instance at 7 AM BRT (10 AM UTC)"
      - eventBridge:
          schedule: cron(0 22 40 * ? *)
          input:
            action: stop
          description: "Stop RDS instance at 7 PM BRT (10 PM UTC)"