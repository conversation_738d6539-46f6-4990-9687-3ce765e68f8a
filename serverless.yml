# Organization ensures this service is used with the correct Serverless Framework Access Key
org: keeps
# Service name - will be added to AWS resource names
service: keeps-lambda-services

frameworkVersion: '4'

provider:
  name: aws
  runtime: python3.12
  region: us-east-1
  stage: ${opt:stage, 'stage'}
  tags:
    Project: Keeps Lambda Services
    Owner: <EMAIL>
    Environment: ${opt:stage, 'stage'}
  environment:
    BUCKET_NAME: ${env:BUCKET_NAME}
  iamRoleStatements:
    - Effect: Allow
      Action:
        - s3:Get*
        - s3:Put*
        - s3:List*
        - s3:Delete*
      Resource:
        - arn:aws:s3:::${self:custom.bucketName}
        - arn:aws:s3:::${self:custom.bucketName}/*
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - logs:DeleteLogGroup
      Resource: '*'

custom:
  bucketName: ${env:BUCKET_NAME}

functions:
  s3Unzipper:
    handler: functions/s3Unzipper/handler.lambda_handler
    description: Lambda that monitors S3 bucket and automatically extracts ZIP files.
    timeout: 300
    memorySize: 512
    package:
      individually: true
      patterns:
        - functions/s3Unzipper/**
    events:
      - s3:
          bucket: ${self:custom.bucketName}
          event: s3:ObjectCreated:*
          rules:
            - prefix: scorm/
            - suffix: .zip
          existing: true
      - s3:
          bucket: ${self:custom.bucketName}
          event: s3:ObjectCreated:*
          rules:
            - prefix: html/
            - suffix: .zip
          existing: true